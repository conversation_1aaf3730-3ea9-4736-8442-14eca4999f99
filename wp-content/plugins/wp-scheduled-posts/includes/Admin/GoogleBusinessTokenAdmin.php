<?php
/**
 * Google Business Token Management Admin Page
 *
 * This file provides an admin interface to test and manage the Google Business
 * token auto-refresh functionality.
 *
 * @since 2.5.0
 */

namespace WPSP\Admin;


use WPSP\Social\GoogleBusinessTokenTest;

class GoogleBusinessTokenAdmin {

    public function __construct() {
        echo "Hello";
        die();
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_init', array($this, 'handle_actions'));
    }

    /**
     * Add admin menu page
     */
    public function add_admin_menu() {
        add_submenu_page(
            'tools.php',
            'Google Business Token Manager',
            'GB Token Manager',
            'manage_options',
            'google-business-token-manager',
            array($this, 'admin_page')
        );
    }

    /**
     * Handle admin actions
     */
    public function handle_actions() {
        if (!current_user_can('manage_options')) {
            return;
        }

        if (isset($_POST['action']) && wp_verify_nonce($_POST['_wpnonce'], 'gb_token_action')) {
            $action = sanitize_text_field($_POST['action']);
            $profile_key = isset($_POST['profile_key']) ? sanitize_text_field($_POST['profile_key']) : null;

            switch ($action) {
                case 'test_refresh':
                    $this->test_refresh($profile_key);
                    break;
                case 'schedule_cron':
                    $this->schedule_cron($profile_key);
                    break;
                case 'clear_cron':
                    $this->clear_cron($profile_key);
                    break;
                case 'cleanup_all':
                    $this->cleanup_all();
                    break;
                case 'cleanup_linkedin_crons':
                    $this->cleanup_linkedin_crons();
                    break;
            }
        }
    }

    /**
     * Test token refresh
     */
    private function test_refresh($profile_key = null) {
        $results = GoogleBusinessTokenTest::test_token_refresh($profile_key);

        if (is_array($results)) {
            foreach ($results as $key => $result) {
                $type = $result['success'] ? 'success' : 'error';
                add_settings_error('gb_token', 'test_refresh', "Profile {$key}: " . $result['message'], $type);
            }
        } else {
            add_settings_error('gb_token', 'test_refresh', 'No profiles to test', 'error');
        }
    }

    /**
     * Schedule cron for profile
     */
    private function schedule_cron($profile_key) {
        if (!$profile_key) {
            add_settings_error('gb_token', 'schedule_cron', 'Profile key required', 'error');
            return;
        }

        $result = GoogleBusinessTokenTest::schedule_cron($profile_key);
        $type = $result['success'] ? 'success' : 'error';
        add_settings_error('gb_token', 'schedule_cron', $result['message'], $type);
    }

    /**
     * Clear cron for profile
     */
    private function clear_cron($profile_key) {
        if (!$profile_key) {
            add_settings_error('gb_token', 'clear_cron', 'Profile key required', 'error');
            return;
        }

        $result = GoogleBusinessTokenTest::clear_cron($profile_key);
        add_settings_error('gb_token', 'clear_cron', $result['message'], 'success');
    }

    /**
     * Cleanup all crons
     */
    private function cleanup_all() {
        $result = GoogleBusinessTokenTest::cleanup_all_crons();
        add_settings_error('gb_token', 'cleanup_all', $result['message'], 'success');
    }

    /**
     * Cleanup orphaned LinkedIn crons
     */
    private function cleanup_linkedin_crons() {
        $google_business = new \WPSP\Social\GoogleBusiness();
        $google_business->cleanup_orphaned_linkedin_crons();
        add_settings_error('gb_token', 'cleanup_linkedin_crons', 'Orphaned LinkedIn crons cleaned up successfully', 'success');
    }

    /**
     * Admin page content
     */
    public function admin_page() {
        $status = GoogleBusinessTokenTest::get_cron_status();
        $all_crons = GoogleBusinessTokenTest::get_all_crons();
        ?>
        <div class="wrap">
            <h1>Google Business Token Manager</h1>

            <?php settings_errors('gb_token'); ?>

            <div class="card">
                <h2>Profile Status</h2>
                <?php echo GoogleBusinessTokenTest::display_cron_status($status); ?>
            </div>

            <div class="card">
                <h2>Actions</h2>

                <form method="post" style="margin-bottom: 20px;">
                    <?php wp_nonce_field('gb_token_action'); ?>
                    <input type="hidden" name="action" value="test_refresh">
                    <input type="submit" class="button button-primary" value="Test Token Refresh (All Profiles)">
                </form>

                <form method="post" style="margin-bottom: 20px;">
                    <?php wp_nonce_field('gb_token_action'); ?>
                    <input type="hidden" name="action" value="cleanup_all">
                    <input type="submit" class="button button-secondary" value="Cleanup All Crons"
                           onclick="return confirm('Are you sure you want to cleanup all Google Business crons?')">
                </form>

                <form method="post" style="margin-bottom: 20px;">
                    <?php wp_nonce_field('gb_token_action'); ?>
                    <input type="hidden" name="action" value="cleanup_linkedin_crons">
                    <input type="submit" class="button button-secondary" value="Cleanup Orphaned LinkedIn Crons"
                           onclick="return confirm('This will remove LinkedIn cron events that were incorrectly created with Google Business account IDs. Continue?')">
                </form>

                <?php if (!empty($status)): ?>
                <h3>Individual Profile Actions</h3>
                <table class="wp-list-table widefat fixed striped">
                    <thead>
                        <tr>
                            <th>Profile</th>
                            <th>Account ID</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($status as $profile_key => $info): ?>
                        <tr>
                            <td><?php echo esc_html($info['profile_name'] . ' (' . $profile_key . ')'); ?></td>
                            <td><code><?php echo esc_html($info['account_id'] ?: 'Unknown'); ?></code></td>
                            <td>
                                <?php echo $info['profile_status'] ? '<span style="color: green;">Active</span>' : '<span style="color: red;">Inactive</span>'; ?>
                                <?php if ($info['cron_scheduled']): ?>
                                    <br><small>Cron: <?php echo esc_html($info['cron_next_run']); ?></small>
                                    <br><small>Hook: <code><?php echo esc_html($info['hook_name']); ?></code></small>
                                <?php else: ?>
                                    <br><small style="color: orange;">No cron scheduled</small>
                                <?php endif; ?>
                            </td>
                            <td>
                                <form method="post" style="display: inline;">
                                    <?php wp_nonce_field('gb_token_action'); ?>
                                    <input type="hidden" name="action" value="test_refresh">
                                    <input type="hidden" name="profile_key" value="<?php echo esc_attr($profile_key); ?>">
                                    <input type="submit" class="button button-small" value="Test Refresh">
                                </form>

                                <form method="post" style="display: inline;">
                                    <?php wp_nonce_field('gb_token_action'); ?>
                                    <input type="hidden" name="action" value="schedule_cron">
                                    <input type="hidden" name="profile_key" value="<?php echo esc_attr($profile_key); ?>">
                                    <input type="submit" class="button button-small" value="Schedule Cron">
                                </form>

                                <form method="post" style="display: inline;">
                                    <?php wp_nonce_field('gb_token_action'); ?>
                                    <input type="hidden" name="action" value="clear_cron">
                                    <input type="hidden" name="profile_key" value="<?php echo esc_attr($profile_key); ?>">
                                    <input type="submit" class="button button-small" value="Clear Cron">
                                </form>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
                <?php endif; ?>
            </div>

            <div class="card">
                <h2>Scheduled Cron Jobs</h2>
                <?php if (empty($all_crons)): ?>
                    <p>No Google Business cron jobs scheduled.</p>
                <?php else: ?>
                    <table class="wp-list-table widefat fixed striped">
                        <thead>
                            <tr>
                                <th>Timestamp</th>
                                <th>Scheduled Time</th>
                                <th>Hook</th>
                                <th>Arguments</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($all_crons as $timestamp => $cron_jobs): ?>
                                <?php foreach ($cron_jobs as $hook => $jobs): ?>
                                    <?php foreach ($jobs as $job): ?>
                                    <tr>
                                        <td><?php echo esc_html($timestamp); ?></td>
                                        <td><?php echo esc_html(date('Y-m-d H:i:s', $timestamp)); ?></td>
                                        <td><?php echo esc_html($hook); ?></td>
                                        <td><?php echo esc_html(print_r($job['args'], true)); ?></td>
                                    </tr>
                                    <?php endforeach; ?>
                                <?php endforeach; ?>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php endif; ?>
            </div>

            <div class="card">
                <h2>Instructions</h2>
                <ol>
                    <li><strong>Profile Status:</strong> Shows all Google Business profiles and their token refresh status.</li>
                    <li><strong>Test Token Refresh:</strong> Manually triggers token refresh for testing purposes.</li>
                    <li><strong>Schedule Cron:</strong> Manually schedules a cron job for token refresh.</li>
                    <li><strong>Clear Cron:</strong> Removes scheduled cron jobs for a profile.</li>
                    <li><strong>Cleanup All Crons:</strong> Removes all Google Business token refresh cron jobs.</li>
                </ol>

                <h3>How It Works</h3>
                <ul>
                    <li>When a Google Business profile is added/updated, a cron job is automatically scheduled.</li>
                    <li>The cron runs 1 hour before the access token expires.</li>
                    <li>If token refresh succeeds, a new cron is scheduled for the next expiry.</li>
                    <li>If token refresh fails, it retries in 1 hour.</li>
                    <li>When a profile is removed, its cron job is automatically cleaned up.</li>
                </ul>
            </div>
        </div>
        <?php
    }
}

// Initialize the admin page
new GoogleBusinessTokenAdmin();
