<?php
namespace WPSP\Social;

use WPSP\Helper;
use WPSP\Traits\SocialHelper;

class GoogleBusiness {
    use SocialHelper;
    private $is_show_meta;
    private $content_type;
    private $is_category_as_tags;
    private $content_source;
    private $template_structure;
    private $status_limit;
    private $post_share_limit;
    private $remove_css_from_content;

    public function __construct() {
        $settings = Helper::get_settings('social_templates');
        $settings = json_decode(json_encode($settings->google_business), true);
        $this->is_show_meta = isset($settings['is_show_meta']) ? $settings['is_show_meta'] : false;
        $this->content_type = isset($settings['content_type']) ? $settings['content_type'] : 'excerpt';
        $this->is_category_as_tags = isset($settings['is_category_as_tags']) ? $settings['is_category_as_tags'] : false;
        $this->content_source = isset($settings['content_source']) ? $settings['content_source'] : 'post_content';
        $this->template_structure = isset($settings['template_structure']) ? $settings['template_structure'] : '{title}{content}{url}';
        $this->status_limit = isset($settings['status_limit']) ? $settings['status_limit'] : 1450;
        $this->post_share_limit = isset($settings['post_share_limit']) ? $settings['post_share_limit'] : 0;
        $this->remove_css_from_content = isset($settings['remove_css_from_content']) ? $settings['remove_css_from_content'] : true;
    }

    public function instance()
    {
        // Schedule Hooks
        add_action('wpsp_publish_future_post', array($this, 'WpScp_GoogleBusiness_post'), 10, 1);
        add_action('wpsp_schedule_republish_share', array($this, 'wpscp_pro_republish_google_business_post'), 10, 1);

        // Token refresh hooks
        add_action('init', array($this, 'register_token_refresh_hooks'));
        add_action('init', array($this, 'initialize_existing_profiles'));
    }

    /**
     * Main share method
     * all logic written here
     * @since 2.5.0
     * @return array
     */
    public function remote_post($app_id, $app_secret, $app_access_token, $type, $ID, $post_id, $profile_key, $force_share = false) {
        // Early returns for conditions that prevent sharing
        $count_meta_key = '__wpsp_google_business_share_count_' . $ID;
        $dont_share = get_post_meta($post_id, '_wpscppro_dont_share_socialmedia', true);

        // Check custom share type
        $get_share_type = get_post_meta($post_id, '_google_business_share_type', true);
        if ($get_share_type === 'custom') {
            $get_all_selected_profile = get_post_meta($post_id, '_selected_social_profile', true);
            if (!Helper::is_profile_exits($ID, $get_all_selected_profile)) {
                return;
            }
        }

        // Check if sharing is disabled
        if (empty($app_id) || (empty($app_access_token) && empty($app_secret)) || $dont_share == 'on' || $dont_share == 1) {
            return;
        }

        // Check share limit
        $share_count = (int)get_post_meta($post_id, $count_meta_key, true);
        if ($share_count && $this->post_share_limit > 0 && $share_count >= $this->post_share_limit) {
            return [
                'success' => false,
                'log' => __('Your max share post limit has been executed!!', 'wp-scheduled-posts')
            ];
        }

        // Check if sharing is enabled for this post
        if (get_post_meta($post_id, '_wpsp_is_google_business_share', true) != 'on' && !$force_share) {
            return [
                'success' => false,
                'log' => __('Google Business share is not enabled for this post', 'wp-scheduled-posts')
            ];
        }

        try {
            // Get post data
            $title   = get_the_title($post_id);
            $content = get_post_field('post_content', $post_id);
            $clean_content = wp_strip_all_tags($content);
            $clean_content = wp_trim_words($clean_content, 30, '...'); // limit to 30 words
            $permalink = get_permalink($post_id);

            // Get featured image
            $featured_image_url = '';
            if (has_post_thumbnail($post_id)) {
                $featured_image = wp_get_attachment_image_src(get_post_thumbnail_id($post_id), 'full');
                $featured_image_url = $featured_image[0];
            }

            $featured_image_url = 'https://images.pexels.com/photos/30625358/pexels-photo-30625358/free-photo-of-cyclists-on-scenic-mountain-trail-with-tv-tower.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2';

            // Refresh token if needed
            $google_business = Helper::get_social_profile(WPSCP_GOOGLE_BUSINESS_OPTION_NAME);
            $refresh_token = !empty($google_business[$profile_key]->refresh_token) ? $google_business[$profile_key]->refresh_token : '';

            if (!empty($refresh_token)) {
                $social_profile = new SocialProfile();
                $refresh_result = $social_profile->refreshGoogleAccessToken($app_id, $app_secret, $refresh_token);

                if (!$refresh_result['error'] && !empty($refresh_result['access_token'])) {
                    $app_access_token = $refresh_result['access_token'];

                    // Update stored token
                    $google_business[$profile_key]->access_token = $app_access_token;
                    $google_business[$profile_key]->expires_in = time() + $refresh_result['expires_in'];
                    update_option(WPSCP_GOOGLE_BUSINESS_OPTION_NAME, $google_business);
                }
            }

            // Get account and location IDs
            $account_id = !empty($google_business[$profile_key]->id) ? $google_business[$profile_key]->id : '';
            $location_id = !empty($google_business[$profile_key]->location_id) ? $google_business[$profile_key]->location_id : '';

            if (empty($location_id) || empty($account_id)) {
                return [
                    'success' => false,
                    'log' => __('Location or Account ID is missing', 'wp-scheduled-posts')
                ];
            }

            // Prepare API request
            $api_url = "https://mybusiness.googleapis.com/v4/{$account_id}/{$location_id}/localPosts";

            $post_data = [
                'languageCode' => 'en',
                'summary'      => $this->get_formatted_text($post_id),   // Combine title and cleaned content
                'topicType'    => 'STANDARD'
            ];

            if( !empty($permalink) ) {
                $post_data['callToAction'] = [
                    'actionType' => 'LEARN_MORE',
                    'url'        => 'https://schedulepress.com',
                ];
            }

            if (!empty($featured_image_url)) {
                $post_data['media'] = [[
                    'mediaFormat' => 'PHOTO',
                    'sourceUrl'   => $featured_image_url,
                ]];
            }

            // Make API request
            $response = wp_remote_post($api_url, [
                'headers' => [
                    'Authorization' => 'Bearer ' . trim($app_access_token),
                    'Content-Type' => 'application/json'
                ],
                'body' => json_encode($post_data),
                'timeout' => 20,
            ]);

            if (is_wp_error($response)) {
                return [
                    'success' => false,
                    'log' => __('Request failed: ', 'wp-scheduled-posts') . $response->get_error_message()
                ];
            }

            $response_code = wp_remote_retrieve_response_code($response);
            $response_body = json_decode(wp_remote_retrieve_body($response), true);

            if ($response_code >= 200 && $response_code < 300) {
                // Update share count
                update_post_meta($post_id, $count_meta_key, $share_count + 1);

                // Extract share ID
                $share_id = '';
                if (!empty($response_body['name'])) {
                    $parts = explode('/', $response_body['name']);
                    $share_id = end($parts);
                }

                return [
                    'success' => true,
                    'log' => [
                        'share_id' => $share_id,
                        'publish_date' => time(),
                    ],
                ];
            } else {
                // Handle API error
                $error_message = isset($response_body['error']['message']) ? $response_body['error']['message'] : 'Unknown error';
                $details = $this->format_error_message($response_body);

                return [
                    'success' => false,
                    'log' => sprintf(__('Failed to share on Google Business: %s', 'wp-scheduled-posts'), $error_message . "\n" . $details)
                ];
            }

        } catch (\Exception $e) {
            return [
                'success' => false,
                'log' => sprintf(__('Exception when sharing to Google Business: %s', 'wp-scheduled-posts'), $e->getMessage())
            ];
        }
    }

    public function get_formatted_text($post_id)
    {
        $post_details = get_post($post_id);
        $title = get_the_title($post_id);
        $post_link = esc_url(get_permalink($post_id));

        // Get content based on source setting
        if ($this->content_source === 'excerpt' && has_excerpt($post_details->ID)) {
            $desc = wp_strip_all_tags($post_details->post_excerpt);
        } else {
            $desc = $this->format_plain_text_with_paragraphs($post_details->post_content);
            if (is_visual_composer_post($post_id) && class_exists('WPBMap')) {
                \WPBMap::addAllMappedShortcodes();
                $desc = Helper::strip_all_html_and_keep_single_breaks(do_shortcode($desc));
            }
        }

        // Apply status limit
        if (!empty($this->status_limit) && strlen($desc) > $this->status_limit) {
            $desc = substr($desc, 0, $this->status_limit - 3) . '...';
        }

        // Format the text according to template structure
        $formatted_text = $this->template_structure;
        $formatted_text = str_replace('{title}', $title . "\n", $formatted_text);
        $formatted_text = str_replace('{content}', $desc . "\n", $formatted_text);
        $formatted_text = str_replace('{url}', $post_link . "\n", $formatted_text);

        // Add tags if enabled
        // if ($this->is_category_as_tags) {
        //     $tags = $this->get_post_tags_comma($post_id);
        //     $formatted_text = str_replace('{tags}', $tags, $formatted_text);
        // } else {
        //     $formatted_text = str_replace('{tags}', '', $formatted_text);
        // }

        // Apply final status limit check
        if (!empty($this->status_limit) && strlen($formatted_text) > $this->status_limit) {
            $formatted_text = substr($formatted_text, 0, $this->status_limit - 10) . '...';
        }

        return $formatted_text;
    }

    public function format_plain_text_with_paragraphs( $content ) {
        // Convert HTML breaks and block elements into double line breaks
        $content = str_ireplace( [ '</p>', '</div>', '<br>', '<br/>', '<br />' ], "\n\n", $content );

        // Strip all remaining HTML tags
        $content = wp_strip_all_tags( $content );

        // Normalize newlines
        $content = str_replace( [ "\r\n", "\r" ], "\n", $content );

        // Collapse 3+ newlines into just 2
        $content = preg_replace( "/\n{3,}/", "\n\n", $content );

        // Trim each line and rebuild
        $lines = array_map( 'trim', explode( "\n", $content ) );
        $content = implode( "\n", $lines );

        return trim( $content );
    }

    public function format_error_message($response_body) {
        $details = '';
        if (!empty($response_body['error']['details'][0]['errorDetails'])) {
            foreach ($response_body['error']['details'][0]['errorDetails'] as $detail) {
                $field   = isset($detail['field']) ? $detail['field'] : '';
                $message = isset($detail['message']) ? $detail['message'] : '';
                $value   = isset($detail['value']) ? $detail['value'] : '';
                $code    = isset($detail['code']) ? $detail['code'] : '';
                $subCode = isset($detail['subErrorCode']) ? $detail['subErrorCode'] : '';
                $details .= "\nField: $field\nMessage: $message\nValue: $value\nCode: $code\nSubCode: $subCode\n";
            }
        }
        return $details;
    }

    /**
     * Schedule Republish social share hook
     * @since 2.5.0
     * @return void
     */
    public function wpscp_pro_republish_google_business_post($post_id) {
        $profiles = Helper::get_social_profile(WPSCP_GOOGLE_BUSINESS_OPTION_NAME);
        if (is_array($profiles) && count($profiles) > 0) {
            foreach ($profiles as $profile_key => $profile) {
                // skip if status is false
                if ($profile->status == false) {
                    continue;
                }
                // call social share method
                $this->remote_post(
                    $profile->app_id,
                    $profile->app_secret,
                    $profile->access_token,
                    $profile->type,
                    $profile->id,
                    $post_id,
                    $profile_key,
                    true
                );
            }
        }
    }

    /**
     * Schedule Future post publish
     * @since 2.5.0
     * @return void
     */
    public function WpScp_GoogleBusiness_post($post_id) {
        $profiles = Helper::get_social_profile(WPSCP_GOOGLE_BUSINESS_OPTION_NAME);
        if (is_array($profiles) && count($profiles) > 0) {
            foreach ($profiles as $profile_key => $profile) {
                if ($profile->status == false) {
                    continue;
                }
                // call social share method
                $this->remote_post(
                    $profile->app_id,
                    $profile->app_secret,
                    $profile->access_token,
                    $profile->type,
                    $profile->id,
                    $post_id,
                    $profile_key,
                    true
                );
            }
        }
    }

    /**
     * This method Call for Instant social share - it will be happend by ajax call
     * @since 2.5.0
     * @return ajax response
     */
    public function socialMediaInstantShare($app_id, $app_secret, $app_access_token, $type, $ID, $post_id, $profile_key, $is_share_on_publish = false) {
        $response = $this->remote_post($app_id, $app_secret, $app_access_token, $type, $ID, $post_id, $profile_key, true);
        if ($is_share_on_publish) {
            return;
        }
        if ($response['success'] == false) {
            wp_send_json_error($response['log']);
        } else {
            wp_send_json_success($response['log']);
        }
    }

    /**
     * Register token refresh hooks for all Google Business profiles
     * @since 2.5.0
     * @return void
     */
    public function register_token_refresh_hooks() {
        $profiles = Helper::get_social_profile(WPSCP_GOOGLE_BUSINESS_OPTION_NAME);
        if (is_array($profiles) && count($profiles) > 0) {
            foreach ($profiles as $profile_key => $profile) {
                $hook_name = 'wpsp_google_business_token_refresh_' . $profile_key;
                add_action($hook_name, array($this, 'refresh_access_token_cron'), 10, 1);
            }
        }
    }

    /**
     * Schedule token refresh cron for a specific profile
     * @since 2.5.0
     * @param string $profile_key
     * @param object $profile
     * @return void
     */
    public function schedule_token_refresh($profile_key, $profile) {
        if (empty($profile->refresh_token) || empty($profile->expires_in)) {
            return;
        }

        $hook_name = 'wpsp_google_business_token_refresh_' . $profile_key;

        // Clear any existing scheduled event
        $this->clear_token_refresh_cron($profile_key);

        // Calculate when to refresh (1 hour before expiry)
        $refresh_time = $profile->expires_in - 3600; // 1 hour before expiry
        $current_time = time();

        // If token expires in less than 1 hour, schedule for immediate refresh
        if ($refresh_time <= $current_time) {
            $refresh_time = $current_time + 60; // 1 minute from now
        }

        // Schedule the cron event
        wp_schedule_single_event($refresh_time, $hook_name, array($profile_key));

        // Log the scheduling
        error_log("WPSP: Scheduled Google Business token refresh for profile {$profile_key} at " . date('Y-m-d H:i:s', $refresh_time));
    }

    /**
     * Clear token refresh cron for a specific profile
     * @since 2.5.0
     * @param string $profile_key
     * @return void
     */
    public function clear_token_refresh_cron($profile_key) {
        $hook_name = 'wpsp_google_business_token_refresh_' . $profile_key;
        $timestamp = wp_next_scheduled($hook_name, array($profile_key));

        if ($timestamp) {
            wp_unschedule_event($timestamp, $hook_name, array($profile_key));
            error_log("WPSP: Cleared Google Business token refresh cron for profile {$profile_key}");
        }
    }

    /**
     * Cron callback to refresh access token
     * @since 2.5.0
     * @param string $profile_key
     * @return void
     */
    public function refresh_access_token_cron($profile_key) {
        $profiles = Helper::get_social_profile(WPSCP_GOOGLE_BUSINESS_OPTION_NAME);

        if (!isset($profiles[$profile_key])) {
            error_log("WPSP: Google Business profile {$profile_key} not found for token refresh");
            return;
        }

        $profile = $profiles[$profile_key];

        if (empty($profile->refresh_token) || empty($profile->app_id) || empty($profile->app_secret)) {
            error_log("WPSP: Missing required data for Google Business token refresh for profile {$profile_key}");
            return;
        }

        try {
            $social_profile = new SocialProfile();
            $refresh_result = $social_profile->refreshGoogleAccessToken(
                $profile->app_id,
                $profile->app_secret,
                $profile->refresh_token
            );

            if (!$refresh_result['error'] && !empty($refresh_result['access_token'])) {
                // Update the profile with new token
                $profiles[$profile_key]->access_token = $refresh_result['access_token'];
                $profiles[$profile_key]->expires_in = time() + $refresh_result['expires_in'];

                // Save updated profiles
                update_option(WPSCP_GOOGLE_BUSINESS_OPTION_NAME, $profiles);

                // Schedule next refresh
                $this->schedule_token_refresh($profile_key, $profiles[$profile_key]);

                error_log("WPSP: Successfully refreshed Google Business token for profile {$profile_key}");
            } else {
                $error_message = isset($refresh_result['message']) ? $refresh_result['message'] : 'Unknown error';
                error_log("WPSP: Failed to refresh Google Business token for profile {$profile_key}: {$error_message}");

                // Schedule retry in 1 hour
                $retry_time = time() + 3600;
                $hook_name = 'wpsp_google_business_token_refresh_' . $profile_key;
                wp_schedule_single_event($retry_time, $hook_name, array($profile_key));
            }
        } catch (\Exception $e) {
            error_log("WPSP: Exception during Google Business token refresh for profile {$profile_key}: " . $e->getMessage());

            // Schedule retry in 1 hour
            $retry_time = time() + 3600;
            $hook_name = 'wpsp_google_business_token_refresh_' . $profile_key;
            wp_schedule_single_event($retry_time, $hook_name, array($profile_key));
        }
    }

    /**
     * Clean up all Google Business token refresh crons
     * @since 2.5.0
     * @return void
     */
    public function cleanup_all_token_refresh_crons() {
        $profiles = Helper::get_social_profile(WPSCP_GOOGLE_BUSINESS_OPTION_NAME);
        if (is_array($profiles) && count($profiles) > 0) {
            foreach ($profiles as $profile_key => $profile) {
                $this->clear_token_refresh_cron($profile_key);
            }
        }

        // Also clean up any orphaned cron events
        $this->cleanup_orphaned_crons();
    }

    /**
     * Clean up orphaned cron events
     * @since 2.5.0
     * @return void
     */
    private function cleanup_orphaned_crons() {
        $crons = _get_cron_array();
        $profiles = Helper::get_social_profile(WPSCP_GOOGLE_BUSINESS_OPTION_NAME);
        $valid_profile_keys = is_array($profiles) ? array_keys($profiles) : array();

        foreach ($crons as $timestamp => $cron_jobs) {
            foreach ($cron_jobs as $hook => $jobs) {
                if (strpos($hook, 'wpsp_google_business_token_refresh_') === 0) {
                    $profile_key = str_replace('wpsp_google_business_token_refresh_', '', $hook);

                    if (!in_array($profile_key, $valid_profile_keys)) {
                        wp_unschedule_event($timestamp, $hook, array($profile_key));
                        error_log("WPSP: Cleaned up orphaned Google Business token refresh cron for profile {$profile_key}");
                    }
                }
            }
        }
    }

    /**
     * Initialize existing profiles to schedule token refresh crons
     * This runs once when the plugin loads to ensure existing profiles have crons scheduled
     * @since 2.5.0
     * @return void
     */
    public function initialize_existing_profiles() {
        // Only run this once per request
        static $initialized = false;
        if ($initialized) {
            return;
        }
        $initialized = true;

        $profiles = Helper::get_social_profile(WPSCP_GOOGLE_BUSINESS_OPTION_NAME);
        if (is_array($profiles) && count($profiles) > 0) {
            foreach ($profiles as $profile_key => $profile) {
                // Only schedule if profile is active and has required data
                if (
                    isset($profile->status) && $profile->status &&
                    isset($profile->refresh_token) && !empty($profile->refresh_token) &&
                    isset($profile->expires_in) && !empty($profile->expires_in)
                ) {
                    // Check if cron is already scheduled
                    $hook_name = 'wpsp_google_business_token_refresh_' . $profile_key;
                    $timestamp = wp_next_scheduled($hook_name, array($profile_key));

                    if (!$timestamp) {
                        // No cron scheduled, schedule one
                        $this->schedule_token_refresh($profile_key, $profile);
                    }
                }
            }
        }
    }

    /**
     * Manual method to trigger token refresh for testing
     * @since 2.5.0
     * @param string $profile_key
     * @return array
     */
    public function manual_refresh_token($profile_key) {
        $profiles = Helper::get_social_profile(WPSCP_GOOGLE_BUSINESS_OPTION_NAME);

        if (!isset($profiles[$profile_key])) {
            return [
                'success' => false,
                'message' => "Profile {$profile_key} not found"
            ];
        }

        $profile = $profiles[$profile_key];

        if (empty($profile->refresh_token) || empty($profile->app_id) || empty($profile->app_secret)) {
            return [
                'success' => false,
                'message' => "Missing required data for token refresh"
            ];
        }

        try {
            $social_profile = new SocialProfile();
            $refresh_result = $social_profile->refreshGoogleAccessToken(
                $profile->app_id,
                $profile->app_secret,
                $profile->refresh_token
            );

            if (!$refresh_result['error'] && !empty($refresh_result['access_token'])) {
                // Update the profile with new token
                $profiles[$profile_key]->access_token = $refresh_result['access_token'];
                $profiles[$profile_key]->expires_in = time() + $refresh_result['expires_in'];

                // Save updated profiles
                update_option(WPSCP_GOOGLE_BUSINESS_OPTION_NAME, $profiles);

                // Schedule next refresh
                $this->schedule_token_refresh($profile_key, $profiles[$profile_key]);

                return [
                    'success' => true,
                    'message' => "Token refreshed successfully",
                    'new_expires_in' => $profiles[$profile_key]->expires_in,
                    'expires_at' => date('Y-m-d H:i:s', $profiles[$profile_key]->expires_in)
                ];
            } else {
                $error_message = isset($refresh_result['message']) ? $refresh_result['message'] : 'Unknown error';
                return [
                    'success' => false,
                    'message' => "Failed to refresh token: {$error_message}"
                ];
            }
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => "Exception during token refresh: " . $e->getMessage()
            ];
        }
    }

    /**
     * Get status of all scheduled cron jobs for Google Business profiles
     * @since 2.5.0
     * @return array
     */
    public function get_cron_status() {
        $profiles = Helper::get_social_profile(WPSCP_GOOGLE_BUSINESS_OPTION_NAME);
        $status = array();

        if (is_array($profiles) && count($profiles) > 0) {
            foreach ($profiles as $profile_key => $profile) {
                $hook_name = 'wpsp_google_business_token_refresh_' . $profile_key;
                $timestamp = wp_next_scheduled($hook_name, array($profile_key));

                $status[$profile_key] = array(
                    'profile_name' => isset($profile->name) ? $profile->name : 'Unknown',
                    'profile_status' => isset($profile->status) ? $profile->status : false,
                    'has_refresh_token' => !empty($profile->refresh_token),
                    'expires_in' => isset($profile->expires_in) ? $profile->expires_in : null,
                    'expires_at' => isset($profile->expires_in) ? date('Y-m-d H:i:s', $profile->expires_in) : null,
                    'cron_scheduled' => $timestamp !== false,
                    'cron_next_run' => $timestamp ? date('Y-m-d H:i:s', $timestamp) : null,
                    'cron_timestamp' => $timestamp
                );
            }
        }

        return $status;
    }
}
