<?php
namespace WPSP\Social;

use WPSP\Helper;
use WPSP\Traits\SocialHelper;

class GoogleBusiness {
    use SocialHelper;
    private $is_show_meta;
    private $content_type;
    private $is_category_as_tags;
    private $content_source;
    private $template_structure;
    private $status_limit;
    private $post_share_limit;
    private $remove_css_from_content;

    public function __construct() {
        $settings = Helper::get_settings('social_templates');
        $settings = json_decode(json_encode($settings->google_business), true);
        $this->is_show_meta = isset($settings['is_show_meta']) ? $settings['is_show_meta'] : false;
        $this->content_type = isset($settings['content_type']) ? $settings['content_type'] : 'excerpt';
        $this->is_category_as_tags = isset($settings['is_category_as_tags']) ? $settings['is_category_as_tags'] : false;
        $this->content_source = isset($settings['content_source']) ? $settings['content_source'] : 'post_content';
        $this->template_structure = isset($settings['template_structure']) ? $settings['template_structure'] : '{title}{content}{url}';
        $this->status_limit = isset($settings['status_limit']) ? $settings['status_limit'] : 1450;
        $this->post_share_limit = isset($settings['post_share_limit']) ? $settings['post_share_limit'] : 0;
        $this->remove_css_from_content = isset($settings['remove_css_from_content']) ? $settings['remove_css_from_content'] : true;
    }

    public function instance()
    {
        // Schedule Hooks
        add_action('wpsp_publish_future_post', array($this, 'WpScp_GoogleBusiness_post'), 10, 1);
        add_action('wpsp_schedule_republish_share', array($this, 'wpscp_pro_republish_google_business_post'), 10, 1);

        // Token refresh hooks
        add_action('init', array($this, 'init_token_refresh'));
    }

    /**
     * Main share method
     * all logic written here
     * @since 2.5.0
     * @return array
     */
    public function remote_post($app_id, $app_secret, $app_access_token, $type, $ID, $post_id, $profile_key, $force_share = false) {
        // Early returns for conditions that prevent sharing
        $count_meta_key = '__wpsp_google_business_share_count_' . $ID;
        $dont_share = get_post_meta($post_id, '_wpscppro_dont_share_socialmedia', true);

        // Check custom share type
        $get_share_type = get_post_meta($post_id, '_google_business_share_type', true);
        if ($get_share_type === 'custom') {
            $get_all_selected_profile = get_post_meta($post_id, '_selected_social_profile', true);
            if (!Helper::is_profile_exits($ID, $get_all_selected_profile)) {
                return;
            }
        }

        // Check if sharing is disabled
        if (empty($app_id) || (empty($app_access_token) && empty($app_secret)) || $dont_share == 'on' || $dont_share == 1) {
            return;
        }

        // Check share limit
        $share_count = (int)get_post_meta($post_id, $count_meta_key, true);
        if ($share_count && $this->post_share_limit > 0 && $share_count >= $this->post_share_limit) {
            return [
                'success' => false,
                'log' => __('Your max share post limit has been executed!!', 'wp-scheduled-posts')
            ];
        }

        // Check if sharing is enabled for this post
        if (get_post_meta($post_id, '_wpsp_is_google_business_share', true) != 'on' && !$force_share) {
            return [
                'success' => false,
                'log' => __('Google Business share is not enabled for this post', 'wp-scheduled-posts')
            ];
        }

        try {
            // Get post data
            $title   = get_the_title($post_id);
            $content = get_post_field('post_content', $post_id);
            $clean_content = wp_strip_all_tags($content);
            $clean_content = wp_trim_words($clean_content, 30, '...'); // limit to 30 words
            $permalink = get_permalink($post_id);

            // Get featured image
            $featured_image_url = '';
            if (has_post_thumbnail($post_id)) {
                $featured_image = wp_get_attachment_image_src(get_post_thumbnail_id($post_id), 'full');
                $featured_image_url = $featured_image[0];
            }

            $featured_image_url = 'https://images.pexels.com/photos/30625358/pexels-photo-30625358/free-photo-of-cyclists-on-scenic-mountain-trail-with-tv-tower.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2';

            // Refresh token if needed
            $google_business = Helper::get_social_profile(WPSCP_GOOGLE_BUSINESS_OPTION_NAME);
            $refresh_token = !empty($google_business[$profile_key]->refresh_token) ? $google_business[$profile_key]->refresh_token : '';

            if (!empty($refresh_token)) {
                $social_profile = new SocialProfile();
                $refresh_result = $social_profile->refreshGoogleAccessToken($app_id, $app_secret, $refresh_token);

                if (!$refresh_result['error'] && !empty($refresh_result['access_token'])) {
                    $app_access_token = $refresh_result['access_token'];

                    // Update stored token
                    $google_business[$profile_key]->access_token = $app_access_token;
                    $google_business[$profile_key]->expires_in = time() + $refresh_result['expires_in'];
                    update_option(WPSCP_GOOGLE_BUSINESS_OPTION_NAME, $google_business);
                }
            }

            // Get account and location IDs
            $account_id = !empty($google_business[$profile_key]->id) ? $google_business[$profile_key]->id : '';
            $location_id = !empty($google_business[$profile_key]->location_id) ? $google_business[$profile_key]->location_id : '';

            if (empty($location_id) || empty($account_id)) {
                return [
                    'success' => false,
                    'log' => __('Location or Account ID is missing', 'wp-scheduled-posts')
                ];
            }

            // Prepare API request
            $api_url = "https://mybusiness.googleapis.com/v4/{$account_id}/{$location_id}/localPosts";

            $post_data = [
                'languageCode' => 'en',
                'summary'      => $this->get_formatted_text($post_id),   // Combine title and cleaned content
                'topicType'    => 'STANDARD'
            ];

            if( !empty($permalink) ) {
                $post_data['callToAction'] = [
                    'actionType' => 'LEARN_MORE',
                    'url'        => 'https://schedulepress.com',
                ];
            }

            if (!empty($featured_image_url)) {
                $post_data['media'] = [[
                    'mediaFormat' => 'PHOTO',
                    'sourceUrl'   => $featured_image_url,
                ]];
            }

            // Make API request
            $response = wp_remote_post($api_url, [
                'headers' => [
                    'Authorization' => 'Bearer ' . trim($app_access_token),
                    'Content-Type' => 'application/json'
                ],
                'body' => json_encode($post_data),
                'timeout' => 20,
            ]);

            if (is_wp_error($response)) {
                return [
                    'success' => false,
                    'log' => __('Request failed: ', 'wp-scheduled-posts') . $response->get_error_message()
                ];
            }

            $response_code = wp_remote_retrieve_response_code($response);
            $response_body = json_decode(wp_remote_retrieve_body($response), true);

            if ($response_code >= 200 && $response_code < 300) {
                // Update share count
                update_post_meta($post_id, $count_meta_key, $share_count + 1);

                // Extract share ID
                $share_id = '';
                if (!empty($response_body['name'])) {
                    $parts = explode('/', $response_body['name']);
                    $share_id = end($parts);
                }

                return [
                    'success' => true,
                    'log' => [
                        'share_id' => $share_id,
                        'publish_date' => time(),
                    ],
                ];
            } else {
                // Handle API error
                $error_message = isset($response_body['error']['message']) ? $response_body['error']['message'] : 'Unknown error';
                $details = $this->format_error_message($response_body);

                return [
                    'success' => false,
                    'log' => sprintf(__('Failed to share on Google Business: %s', 'wp-scheduled-posts'), $error_message . "\n" . $details)
                ];
            }

        } catch (\Exception $e) {
            return [
                'success' => false,
                'log' => sprintf(__('Exception when sharing to Google Business: %s', 'wp-scheduled-posts'), $e->getMessage())
            ];
        }
    }

    public function get_formatted_text($post_id)
    {
        $post_details = get_post($post_id);
        $title = get_the_title($post_id);
        $post_link = esc_url(get_permalink($post_id));

        // Get content based on source setting
        if ($this->content_source === 'excerpt' && has_excerpt($post_details->ID)) {
            $desc = wp_strip_all_tags($post_details->post_excerpt);
        } else {
            $desc = $this->format_plain_text_with_paragraphs($post_details->post_content);
            if (is_visual_composer_post($post_id) && class_exists('WPBMap')) {
                \WPBMap::addAllMappedShortcodes();
                $desc = Helper::strip_all_html_and_keep_single_breaks(do_shortcode($desc));
            }
        }

        // Apply status limit
        if (!empty($this->status_limit) && strlen($desc) > $this->status_limit) {
            $desc = substr($desc, 0, $this->status_limit - 3) . '...';
        }

        // Format the text according to template structure
        $formatted_text = $this->template_structure;
        $formatted_text = str_replace('{title}', $title . "\n", $formatted_text);
        $formatted_text = str_replace('{content}', $desc . "\n", $formatted_text);
        $formatted_text = str_replace('{url}', $post_link . "\n", $formatted_text);

        // Add tags if enabled
        // if ($this->is_category_as_tags) {
        //     $tags = $this->get_post_tags_comma($post_id);
        //     $formatted_text = str_replace('{tags}', $tags, $formatted_text);
        // } else {
        //     $formatted_text = str_replace('{tags}', '', $formatted_text);
        // }

        // Apply final status limit check
        if (!empty($this->status_limit) && strlen($formatted_text) > $this->status_limit) {
            $formatted_text = substr($formatted_text, 0, $this->status_limit - 10) . '...';
        }

        return $formatted_text;
    }

    public function format_plain_text_with_paragraphs( $content ) {
        // Convert HTML breaks and block elements into double line breaks
        $content = str_ireplace( [ '</p>', '</div>', '<br>', '<br/>', '<br />' ], "\n\n", $content );

        // Strip all remaining HTML tags
        $content = wp_strip_all_tags( $content );

        // Normalize newlines
        $content = str_replace( [ "\r\n", "\r" ], "\n", $content );

        // Collapse 3+ newlines into just 2
        $content = preg_replace( "/\n{3,}/", "\n\n", $content );

        // Trim each line and rebuild
        $lines = array_map( 'trim', explode( "\n", $content ) );
        $content = implode( "\n", $lines );

        return trim( $content );
    }

    public function format_error_message($response_body) {
        $details = '';
        if (!empty($response_body['error']['details'][0]['errorDetails'])) {
            foreach ($response_body['error']['details'][0]['errorDetails'] as $detail) {
                $field   = isset($detail['field']) ? $detail['field'] : '';
                $message = isset($detail['message']) ? $detail['message'] : '';
                $value   = isset($detail['value']) ? $detail['value'] : '';
                $code    = isset($detail['code']) ? $detail['code'] : '';
                $subCode = isset($detail['subErrorCode']) ? $detail['subErrorCode'] : '';
                $details .= "\nField: $field\nMessage: $message\nValue: $value\nCode: $code\nSubCode: $subCode\n";
            }
        }
        return $details;
    }

    /**
     * Schedule Republish social share hook
     * @since 2.5.0
     * @return void
     */
    public function wpscp_pro_republish_google_business_post($post_id) {
        $profiles = Helper::get_social_profile(WPSCP_GOOGLE_BUSINESS_OPTION_NAME);
        if (is_array($profiles) && count($profiles) > 0) {
            foreach ($profiles as $profile_key => $profile) {
                // skip if status is false
                if ($profile->status == false) {
                    continue;
                }
                // call social share method
                $this->remote_post(
                    $profile->app_id,
                    $profile->app_secret,
                    $profile->access_token,
                    $profile->type,
                    $profile->id,
                    $post_id,
                    $profile_key,
                    true
                );
            }
        }
    }

    /**
     * Schedule Future post publish
     * @since 2.5.0
     * @return void
     */
    public function WpScp_GoogleBusiness_post($post_id) {
        $profiles = Helper::get_social_profile(WPSCP_GOOGLE_BUSINESS_OPTION_NAME);
        if (is_array($profiles) && count($profiles) > 0) {
            foreach ($profiles as $profile_key => $profile) {
                if ($profile->status == false) {
                    continue;
                }
                // call social share method
                $this->remote_post(
                    $profile->app_id,
                    $profile->app_secret,
                    $profile->access_token,
                    $profile->type,
                    $profile->id,
                    $post_id,
                    $profile_key,
                    true
                );
            }
        }
    }

    /**
     * This method Call for Instant social share - it will be happend by ajax call
     * @since 2.5.0
     * @return ajax response
     */
    public function socialMediaInstantShare($app_id, $app_secret, $app_access_token, $type, $ID, $post_id, $profile_key, $is_share_on_publish = false) {
        $response = $this->remote_post($app_id, $app_secret, $app_access_token, $type, $ID, $post_id, $profile_key, true);
        if ($is_share_on_publish) {
            return;
        }
        if ($response['success'] == false) {
            wp_send_json_error($response['log']);
        } else {
            wp_send_json_success($response['log']);
        }
    }

    public function init_token_refresh() {
        static $initialized = false;
        if ($initialized) return;
        $initialized = true;

        // Register the common hook
        add_action('wpsp_google_business_token_refresh', array($this, 'refresh_access_token_cron'), 10, 1);

        $profiles = Helper::get_social_profile(WPSCP_GOOGLE_BUSINESS_OPTION_NAME);
        if (!is_array($profiles)) return;

        // Initialize profiles
        foreach ($profiles as $profile_key => $profile) {
            if (empty($profile->id)) continue;

            $account_id = strpos($profile->id, 'accounts/') === 0 ? substr($profile->id, 9) : $profile->id;

            // Schedule if needed
            if ($profile->status && !empty($profile->refresh_token) && !empty($profile->expires_in)) {
                if (!wp_next_scheduled('wpsp_google_business_token_refresh', array($account_id))) {
                    $refresh_time = $profile->expires_in - 3600;
                    if ($refresh_time <= time()) $refresh_time = time() + 60;
                    wp_schedule_single_event($refresh_time, 'wpsp_google_business_token_refresh', array($account_id));
                }
            }
        }

        // Cleanup orphaned LinkedIn crons
        // $crons = _get_cron_array();
        // foreach ($crons as $timestamp => $cron_jobs) {
        //     if (isset($cron_jobs['wpsp_linkedin_reconnect_cron_event'])) {
        //         foreach ($cron_jobs['wpsp_linkedin_reconnect_cron_event'] as $job) {
        //             if (isset($job['args'][0])) {
        //                 $cron_id = $job['args'][0];
        //                 foreach ($profiles as $profile) {
        //                     if (isset($profile->id)) {
        //                         $account_id = strpos($profile->id, 'accounts/') === 0 ? substr($profile->id, 9) : $profile->id;
        //                         if ($cron_id === $account_id || $cron_id === $profile->id) {
        //                             wp_unschedule_event($timestamp, 'wpsp_linkedin_reconnect_cron_event', array($cron_id));
        //                             break;
        //                         }
        //                     }
        //                 }
        //             }
        //         }
        //     }
        // }
    }

    public function schedule_token_refresh($profile_key, $profile) {
        if (empty($profile->refresh_token) || empty($profile->expires_in) || empty($profile->id)) return;

        $account_id = strpos($profile->id, 'accounts/') === 0 ? substr($profile->id, 9) : $profile->id;

        // Clear existing
        if ($timestamp = wp_next_scheduled('wpsp_google_business_token_refresh', array($account_id))) {
            wp_unschedule_event($timestamp, 'wpsp_google_business_token_refresh', array($account_id));
        }

        // Schedule new
        $refresh_time = $profile->expires_in - 3600;
        if ($refresh_time <= time()) $refresh_time = time() + 60;
        wp_schedule_single_event($refresh_time, 'wpsp_google_business_token_refresh', array($account_id));
    }

    public function refresh_access_token_cron($account_id) {
        $profiles = Helper::get_social_profile(WPSCP_GOOGLE_BUSINESS_OPTION_NAME);
        if (!is_array($profiles)) return;

        // Find profile by account ID
        $profile_key = null;
        $profile = null;
        foreach ($profiles as $key => $p) {
            if (!empty($p->id)) {
                $p_account_id = strpos($p->id, 'accounts/') === 0 ? substr($p->id, 9) : $p->id;
                if ($p_account_id === $account_id) {
                    $profile_key = $key;
                    $profile = $p;
                    break;
                }
            }
        }

        if (!$profile || empty($profile->refresh_token) || empty($profile->app_id) || empty($profile->app_secret)) return;

        $social_profile = new SocialProfile();
        $refresh_result = $social_profile->refreshGoogleAccessToken(
            $profile->app_id,
            $profile->app_secret,
            $profile->refresh_token
        );

        if (!$refresh_result['error'] && !empty($refresh_result['access_token'])) {
            $profiles[$profile_key]->access_token = $refresh_result['access_token'];
            $profiles[$profile_key]->expires_in = time() + $refresh_result['expires_in'];
            update_option(WPSCP_GOOGLE_BUSINESS_OPTION_NAME, $profiles);
            $this->schedule_token_refresh($profile_key, $profiles[$profile_key]);
        } else {
            // Retry in 1 hour
            wp_schedule_single_event(time() + 3600, 'wpsp_google_business_token_refresh', array($account_id));
        }
    }

}
