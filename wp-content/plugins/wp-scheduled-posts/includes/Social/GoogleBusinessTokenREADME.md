# Google Business Access Token Auto-Generation

This implementation provides automatic access token refresh for Google Business profiles using WordPress cron jobs.

## Features

- **Automatic Token Refresh**: Tokens are automatically refreshed 1 hour before expiry
- **Profile Management**: Cron jobs are automatically created when profiles are added and removed when profiles are deleted
- **Account ID Based Hooks**: Cron hooks use Google Business account IDs for better organization
- **Error Handling**: Failed refreshes are retried with exponential backoff
- **Logging**: All operations are logged for debugging
- **Manual Testing**: Admin interface for testing and monitoring

## How It Works

### 1. Profile Addition
When a Google Business profile is added or updated:
- The system checks if the profile has a refresh token and expiry time
- If valid, a cron job is scheduled to run 1 hour before token expiry
- Each profile gets its own unique cron hook based on account ID: `wpsp_google_business_token_refresh_{account_id}`
- Account ID is extracted from the profile's `id` field (e.g., "accounts/102116315773958835954" becomes "102116315773958835954")

### 2. Token Refresh Process
When the cron job runs:
1. Retrieves the profile's refresh token, app ID, and app secret
2. Calls Google's OAuth2 token refresh endpoint
3. If successful:
   - Updates the profile with the new access token and expiry time
   - Schedules the next refresh cron job
4. If failed:
   - Logs the error
   - Schedules a retry in 1 hour

### 3. Profile Removal
When a Google Business profile is removed:
- The associated cron job is automatically unscheduled
- Orphaned cron jobs are cleaned up periodically

## Cron Job Details

### Hook Names
- Pattern: `wpsp_google_business_token_refresh_{account_id}`
- Example: `wpsp_google_business_token_refresh_102116315773958835954`
- Account ID extracted from profile ID: "accounts/102116315773958835954" → "102116315773958835954"

### Scheduling
- **Initial Schedule**: 1 hour before token expiry
- **Retry Schedule**: 1 hour after failed attempt
- **Type**: Single event (wp_schedule_single_event)

### Arguments
- Each cron job receives the profile key as an argument (for internal reference)

## Key Methods

### GoogleBusiness Class
- `extract_account_id($full_id)` - Extract numeric account ID from full Google ID
- `schedule_token_refresh($profile_key, $profile)` - Schedule cron for a profile
- `clear_token_refresh_cron($profile_key)` - Clear cron for a profile
- `refresh_access_token_cron($profile_key)` - Cron callback for token refresh
- `initialize_existing_profiles()` - Initialize crons for existing profiles
- `manual_refresh_token($profile_key)` - Manual token refresh for testing
- `get_cron_status()` - Get status of all scheduled crons

### SocialProfile Class
- `handle_google_business_profile_changes($old_value, $value, $option)` - Monitor profile changes

## Testing

### Admin Interface
1. Go to **Tools > GB Token Manager** in WordPress admin
2. View profile status, account IDs, and scheduled cron jobs
3. Test token refresh manually
4. Schedule/clear cron jobs for individual profiles

### Manual Testing Functions
```php
// Test token refresh for all profiles
$results = wpsp_test_google_business_token_refresh();
echo $results;

// Show cron status
$status = wpsp_show_google_business_cron_status();
echo $status;

// Cleanup all crons
$result = wpsp_cleanup_google_business_crons();
echo $result;
```

## Example Cron Events

For a Google Business profile with ID "accounts/102116315773958835954":

```bash
# Hook name
wpsp_google_business_token_refresh_102116315773958835954

# WP-CLI commands
wp cron event list --hook=wpsp_google_business_token_refresh_102116315773958835954
wp cron event run wpsp_google_business_token_refresh_102116315773958835954
```

## Error Handling

### Logging
All operations are logged using WordPress's `error_log()` function:
- Successful token refreshes with account ID
- Failed token refreshes with error messages
- Cron scheduling/unscheduling events with account ID
- Profile changes

### Retry Logic
- Failed token refreshes are retried in 1 hour
- No limit on retry attempts (relies on refresh token validity)
- Errors are logged for debugging

## Security Considerations

1. **Refresh Tokens**: Stored securely in WordPress options
2. **Access Control**: Admin functions require `manage_options` capability
3. **Nonce Verification**: All admin actions use WordPress nonces
4. **Input Sanitization**: All user inputs are sanitized

## Troubleshooting

### Common Issues

1. **Cron Not Running**
   - Check if WordPress cron is enabled
   - Verify server cron configuration
   - Use WP-CLI: `wp cron event list`

2. **Token Refresh Failing**
   - Check refresh token validity
   - Verify app ID and secret
   - Check Google API quotas
   - Review error logs

3. **Profiles Not Scheduling**
   - Ensure profile has refresh token
   - Check profile status (must be active)
   - Verify expiry time is set
   - Check account ID extraction

### Debug Commands
```bash
# List all cron events
wp cron event list

# List Google Business cron events
wp cron event list | grep wpsp_google_business_token_refresh

# Run specific cron manually
wp cron event run wpsp_google_business_token_refresh_102116315773958835954

# Check specific hook
wp cron event list --hook=wpsp_google_business_token_refresh_102116315773958835954
```

## Migration Notes

If you have existing profiles with old cron hooks (using profile keys), the system includes backward compatibility:
- Old hooks: `wpsp_google_business_token_refresh_0`
- New hooks: `wpsp_google_business_token_refresh_102116315773958835954`
- The `clear_token_refresh_cron()` method tries both formats for cleanup

## Files Modified/Created

### Modified Files
1. **`GoogleBusiness.php`** - Added account ID-based cron management
2. **`SocialProfile.php`** - Added profile change monitoring

### New Files
1. **`GoogleBusinessTokenTest.php`** - Test utilities with account ID support
2. **`GoogleBusinessTokenAdmin.php`** - Admin interface showing account IDs
3. **`GoogleBusinessTokenREADME.md`** - This documentation

## Support

For issues or questions:
1. Check the admin interface at **Tools > GB Token Manager**
2. Review WordPress error logs
3. Use the test functions for debugging
4. Check cron job status with WP-CLI
5. Verify account ID extraction is working correctly
