<?php
/**
 * Google Business Token Management Test/Debug Helper
 * 
 * This file provides utility functions to test and debug the Google Business
 * token auto-refresh functionality.
 * 
 * Usage:
 * 1. Add this to your functions.php or create a custom admin page
 * 2. Call the test functions to verify the implementation
 * 
 * @since 2.5.0
 */

namespace WPSP\Social;

use WPSP\Helper;

class GoogleBusinessTokenTest {
    
    /**
     * Test the token refresh functionality
     * 
     * @param string $profile_key Optional profile key to test specific profile
     * @return array Test results
     */
    public static function test_token_refresh($profile_key = null) {
        $google_business = new GoogleBusiness();
        $results = array();
        
        // Get all profiles or specific profile
        $profiles = Helper::get_social_profile(WPSCP_GOOGLE_BUSINESS_OPTION_NAME);
        
        if ($profile_key && isset($profiles[$profile_key])) {
            $profiles = array($profile_key => $profiles[$profile_key]);
        }
        
        if (empty($profiles)) {
            return array(
                'success' => false,
                'message' => 'No Google Business profiles found'
            );
        }
        
        foreach ($profiles as $key => $profile) {
            $result = $google_business->manual_refresh_token($key);
            $results[$key] = $result;
        }
        
        return $results;
    }
    
    /**
     * Get status of all cron jobs
     * 
     * @return array Cron status for all profiles
     */
    public static function get_cron_status() {
        $google_business = new GoogleBusiness();
        return $google_business->get_cron_status();
    }
    
    /**
     * Schedule cron for a specific profile (for testing)
     * 
     * @param string $profile_key
     * @return array Result
     */
    public static function schedule_cron($profile_key) {
        $profiles = Helper::get_social_profile(WPSCP_GOOGLE_BUSINESS_OPTION_NAME);
        
        if (!isset($profiles[$profile_key])) {
            return array(
                'success' => false,
                'message' => "Profile {$profile_key} not found"
            );
        }
        
        $google_business = new GoogleBusiness();
        $google_business->schedule_token_refresh($profile_key, $profiles[$profile_key]);
        
        return array(
            'success' => true,
            'message' => "Cron scheduled for profile {$profile_key}"
        );
    }
    
    /**
     * Clear cron for a specific profile (for testing)
     * 
     * @param string $profile_key
     * @return array Result
     */
    public static function clear_cron($profile_key) {
        $google_business = new GoogleBusiness();
        $google_business->clear_token_refresh_cron($profile_key);
        
        return array(
            'success' => true,
            'message' => "Cron cleared for profile {$profile_key}"
        );
    }
    
    /**
     * Get all WordPress cron events (for debugging)
     * 
     * @return array All cron events
     */
    public static function get_all_crons() {
        $crons = _get_cron_array();
        $google_business_crons = array();
        
        foreach ($crons as $timestamp => $cron_jobs) {
            foreach ($cron_jobs as $hook => $jobs) {
                if (strpos($hook, 'wpsp_google_business_token_refresh_') === 0) {
                    $google_business_crons[$timestamp][$hook] = $jobs;
                }
            }
        }
        
        return $google_business_crons;
    }
    
    /**
     * Clean up all Google Business crons (for testing)
     * 
     * @return array Result
     */
    public static function cleanup_all_crons() {
        $google_business = new GoogleBusiness();
        $google_business->cleanup_all_token_refresh_crons();
        
        return array(
            'success' => true,
            'message' => 'All Google Business crons cleaned up'
        );
    }
    
    /**
     * Display test results in a readable format
     * 
     * @param array $results
     * @return string HTML output
     */
    public static function display_results($results) {
        $output = '<div style="background: #fff; padding: 20px; margin: 20px 0; border: 1px solid #ccc;">';
        $output .= '<h3>Google Business Token Test Results</h3>';
        
        if (is_array($results)) {
            foreach ($results as $key => $result) {
                $status = $result['success'] ? 'SUCCESS' : 'FAILED';
                $color = $result['success'] ? 'green' : 'red';
                
                $output .= '<div style="margin: 10px 0; padding: 10px; border-left: 4px solid ' . $color . ';">';
                $output .= '<strong>Profile ' . $key . ': ' . $status . '</strong><br>';
                $output .= 'Message: ' . $result['message'] . '<br>';
                
                if (isset($result['expires_at'])) {
                    $output .= 'New expiry: ' . $result['expires_at'] . '<br>';
                }
                
                $output .= '</div>';
            }
        } else {
            $output .= '<p>' . print_r($results, true) . '</p>';
        }
        
        $output .= '</div>';
        return $output;
    }
    
    /**
     * Display cron status in a readable format
     * 
     * @param array $status
     * @return string HTML output
     */
    public static function display_cron_status($status) {
        $output = '<div style="background: #fff; padding: 20px; margin: 20px 0; border: 1px solid #ccc;">';
        $output .= '<h3>Google Business Cron Status</h3>';
        
        if (empty($status)) {
            $output .= '<p>No Google Business profiles found.</p>';
        } else {
            $output .= '<table style="width: 100%; border-collapse: collapse;">';
            $output .= '<tr style="background: #f0f0f0;">';
            $output .= '<th style="border: 1px solid #ccc; padding: 8px;">Profile</th>';
            $output .= '<th style="border: 1px solid #ccc; padding: 8px;">Status</th>';
            $output .= '<th style="border: 1px solid #ccc; padding: 8px;">Has Refresh Token</th>';
            $output .= '<th style="border: 1px solid #ccc; padding: 8px;">Token Expires</th>';
            $output .= '<th style="border: 1px solid #ccc; padding: 8px;">Cron Scheduled</th>';
            $output .= '<th style="border: 1px solid #ccc; padding: 8px;">Next Run</th>';
            $output .= '</tr>';
            
            foreach ($status as $profile_key => $info) {
                $output .= '<tr>';
                $output .= '<td style="border: 1px solid #ccc; padding: 8px;">' . $info['profile_name'] . ' (' . $profile_key . ')</td>';
                $output .= '<td style="border: 1px solid #ccc; padding: 8px;">' . ($info['profile_status'] ? 'Active' : 'Inactive') . '</td>';
                $output .= '<td style="border: 1px solid #ccc; padding: 8px;">' . ($info['has_refresh_token'] ? 'Yes' : 'No') . '</td>';
                $output .= '<td style="border: 1px solid #ccc; padding: 8px;">' . ($info['expires_at'] ?: 'Unknown') . '</td>';
                $output .= '<td style="border: 1px solid #ccc; padding: 8px;">' . ($info['cron_scheduled'] ? 'Yes' : 'No') . '</td>';
                $output .= '<td style="border: 1px solid #ccc; padding: 8px;">' . ($info['cron_next_run'] ?: 'Not scheduled') . '</td>';
                $output .= '</tr>';
            }
            
            $output .= '</table>';
        }
        
        $output .= '</div>';
        return $output;
    }
}

// Example usage functions that can be called from WordPress admin or functions.php

/**
 * Test all Google Business token refreshes
 * Usage: Call this function from WordPress admin or add to functions.php temporarily
 */
function wpsp_test_google_business_token_refresh() {
    if (!current_user_can('manage_options')) {
        return 'Access denied';
    }
    
    $results = GoogleBusinessTokenTest::test_token_refresh();
    return GoogleBusinessTokenTest::display_results($results);
}

/**
 * Show Google Business cron status
 * Usage: Call this function from WordPress admin or add to functions.php temporarily
 */
function wpsp_show_google_business_cron_status() {
    if (!current_user_can('manage_options')) {
        return 'Access denied';
    }
    
    $status = GoogleBusinessTokenTest::get_cron_status();
    return GoogleBusinessTokenTest::display_cron_status($status);
}

/**
 * Clean up all Google Business crons
 * Usage: Call this function to clean up all crons (useful for testing)
 */
function wpsp_cleanup_google_business_crons() {
    if (!current_user_can('manage_options')) {
        return 'Access denied';
    }
    
    $result = GoogleBusinessTokenTest::cleanup_all_crons();
    return $result['message'];
}
