[Tu<PERSON>, 27 May 2025 06:19:48 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:19:48 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:19:50 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:19:53 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:20:02 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:20:02 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:20:02 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:20:09 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:20:09 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:20:09 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:20:10 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:21:12 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:22:10 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:23:13 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:24:11 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:25:14 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:26:12 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:27:15 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:28:13 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:29:16 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:30:14 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:31:17 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:32:10 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:32:11 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:32:13 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:32:16 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:32:17 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:32:18 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:32:18 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:32:18 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:32:20 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:32:24 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:32:24 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:32:25 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:32:25 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:32:26 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:32:27 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:32:31 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:32:31 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:32:33 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:32:33 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:32:48 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:32:59 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:33:00 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:33:00 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:33:01 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:33:02 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:33:03 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:33:07 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:33:08 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:33:11 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:33:12 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:33:12 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:33:14 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:33:16 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:33:16 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:33:16 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:33:16 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:33:22 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:33:34 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:33:35 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:33:44 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:33:44 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:33:54 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:34:04 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:34:14 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:34:14 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:34:20 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:34:31 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:34:35 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:34:39 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:34:39 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:34:50 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:35:00 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:35:11 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:35:17 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:35:21 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:35:32 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:35:58 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:36:01 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:36:08 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:36:08 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:36:10 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:36:11 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:36:11 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:36:12 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:36:12 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:36:14 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:36:45 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:36:50 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:36:57 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:36:57 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:36:57 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:36:59 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:36:59 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:37:12 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:37:18 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:37:36 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:37:52 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:37:58 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:38:13 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:38:28 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:38:31 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:38:31 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:38:33 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:38:33 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:38:35 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:38:35 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:39:02 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:39:02 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:39:32 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:39:34 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:39:53 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:41:32 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:41:35 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:41:54 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 06:43:36 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 07:08:20 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 07:08:21 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 07:08:21 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 07:08:35 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 07:08:50 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 07:09:05 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 07:09:20 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 07:09:35 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 07:09:50 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 07:10:05 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 07:10:20 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 07:10:21 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 07:10:35 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 07:51:00 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 07:51:15 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 07:53:17 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 07:55:17 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:11:59 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:11:59 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:12:19 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:13:31 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:13:34 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:13:42 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:14:42 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:15:05 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:15:17 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:15:17 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:15:19 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:15:21 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:15:21 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:15:23 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:15:23 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:15:23 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:15:37 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:15:38 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:15:38 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:15:39 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:15:39 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:15:53 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:16:08 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:16:23 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:17:15 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:17:30 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:17:50 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:18:05 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:18:32 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:18:48 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:19:26 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:19:41 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:20:16 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:20:31 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:20:46 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:20:54 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:20:56 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:20:56 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:21:11 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:21:27 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:21:32 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:21:33 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:21:34 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:21:34 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:21:48 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:21:52 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:21:52 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:22:03 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:22:18 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:22:38 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:22:53 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:23:08 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:23:23 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:23:38 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:23:54 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:24:09 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:24:24 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:24:47 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:25:02 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:25:17 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:25:32 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:25:48 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:27:49 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:28:32 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:28:47 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:29:34 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:29:49 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:30:43 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:30:58 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:32:28 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:32:42 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:33:08 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:33:23 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:35:24 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:35:39 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:35:54 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:36:10 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:36:22 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:36:24 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:36:25 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:36:39 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:36:54 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:36:57 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:36:58 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:36:59 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:37:14 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:37:30 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:37:35 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:37:36 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:37:37 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:37:38 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:37:44 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:37:44 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:37:52 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:38:07 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:38:22 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:38:25 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:38:26 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:38:27 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:38:28 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:38:32 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:38:32 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:38:41 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:38:56 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:39:12 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:40:00 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:40:01 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:40:02 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:40:03 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:40:04 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:40:14 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:40:14 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:40:18 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:40:33 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:40:49 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:42:36 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:42:40 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:42:41 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:42:42 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:42:44 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:42:46 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:42:46 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:42:57 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:43:12 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:43:27 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:43:37 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:43:38 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:43:39 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:43:40 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:43:44 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:43:44 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:43:53 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:44:00 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:44:00 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:44:08 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:44:13 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:44:13 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:44:23 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:44:43 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:44:45 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:44:46 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:44:47 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:44:48 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:44:57 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:44:57 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:45:01 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:45:06 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:45:09 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:45:17 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:47:03 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:47:51 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:48:06 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:51:10 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:51:25 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:53:26 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:57:42 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:57:45 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:58:35 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:58:42 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:58:57 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:58:58 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:59:09 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:59:09 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:59:10 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:59:26 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:59:34 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:59:38 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:59:47 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:59:47 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:59:48 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:59:54 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 08:59:55 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:00:49 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:02:09 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:03:30 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:06:25 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:07:46 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:08:26 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:09:47 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:10:27 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:11:48 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:12:28 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:12:43 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:12:49 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:12:56 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:12:56 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:12:57 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:12:57 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:12:57 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:12:59 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:13:00 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:13:07 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:13:08 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:13:08 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:13:09 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:13:20 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:13:20 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:13:23 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:13:38 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:13:57 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:14:07 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:14:13 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:14:14 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:14:18 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:14:19 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:14:19 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:14:20 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:14:21 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:14:22 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:14:22 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:14:22 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:14:24 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:14:25 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:14:49 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:14:50 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:15:53 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:16:08 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:16:11 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:16:14 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:16:23 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:16:30 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:17:12 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:18:24 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:18:31 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:19:13 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:20:25 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:20:32 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:21:14 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:34:56 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:35:11 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:36:56 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:37:12 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:38:57 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:39:13 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:40:58 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:41:14 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:42:59 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:43:15 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:45:01 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:45:16 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:47:01 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:47:17 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:49:02 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:49:18 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:51:03 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:51:19 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:51:25 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:51:25 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:51:35 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:51:39 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:51:47 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:51:56 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:51:56 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:52:00 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:52:16 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:52:20 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:52:27 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:52:29 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:52:38 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:52:47 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:53:04 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:53:28 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:53:36 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:53:48 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Tue, 27 May 2025 09:54:28 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.04',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/schedulepress/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://schedulepress.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

